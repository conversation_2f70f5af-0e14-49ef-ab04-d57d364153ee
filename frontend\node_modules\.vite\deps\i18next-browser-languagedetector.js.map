{"version": 3, "sources": ["../../i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js"], "sourcesContent": ["const {\n  slice,\n  forEach\n} = [];\nfunction defaults(obj) {\n  forEach.call(slice.call(arguments, 1), source => {\n    if (source) {\n      for (const prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nfunction hasXSS(input) {\n  if (typeof input !== 'string') return false;\n\n  // Common XSS attack patterns\n  const xssPatterns = [/<\\s*script.*?>/i, /<\\s*\\/\\s*script\\s*>/i, /<\\s*img.*?on\\w+\\s*=/i, /<\\s*\\w+\\s*on\\w+\\s*=.*?>/i, /javascript\\s*:/i, /vbscript\\s*:/i, /expression\\s*\\(/i, /eval\\s*\\(/i, /alert\\s*\\(/i, /document\\.cookie/i, /document\\.write\\s*\\(/i, /window\\.location/i, /innerHTML/i];\n  return xssPatterns.some(pattern => pattern.test(input));\n}\n\n// eslint-disable-next-line no-control-regex\nconst fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\nconst serializeCookie = function (name, val) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    path: '/'\n  };\n  const opt = options;\n  const value = encodeURIComponent(val);\n  let str = `${name}=${value}`;\n  if (opt.maxAge > 0) {\n    const maxAge = opt.maxAge - 0;\n    if (Number.isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += `; Max-Age=${Math.floor(maxAge)}`;\n  }\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += `; Domain=${opt.domain}`;\n  }\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += `; Path=${opt.path}`;\n  }\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n    str += `; Expires=${opt.expires.toUTCString()}`;\n  }\n  if (opt.httpOnly) str += '; HttpOnly';\n  if (opt.secure) str += '; Secure';\n  if (opt.sameSite) {\n    const sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  if (opt.partitioned) str += '; Partitioned';\n  return str;\n};\nconst cookie = {\n  create(name, value, minutes, domain) {\n    let cookieOptions = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : {\n      path: '/',\n      sameSite: 'strict'\n    };\n    if (minutes) {\n      cookieOptions.expires = new Date();\n      cookieOptions.expires.setTime(cookieOptions.expires.getTime() + minutes * 60 * 1000);\n    }\n    if (domain) cookieOptions.domain = domain;\n    document.cookie = serializeCookie(name, value, cookieOptions);\n  },\n  read(name) {\n    const nameEQ = `${name}=`;\n    const ca = document.cookie.split(';');\n    for (let i = 0; i < ca.length; i++) {\n      let c = ca[i];\n      while (c.charAt(0) === ' ') c = c.substring(1, c.length);\n      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n  },\n  remove(name, domain) {\n    this.create(name, '', -1, domain);\n  }\n};\nvar cookie$1 = {\n  name: 'cookie',\n  // Deconstruct the options object and extract the lookupCookie property\n  lookup(_ref) {\n    let {\n      lookupCookie\n    } = _ref;\n    if (lookupCookie && typeof document !== 'undefined') {\n      return cookie.read(lookupCookie) || undefined;\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupCookie, cookieMinutes, cookieDomain, and cookieOptions properties\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupCookie,\n      cookieMinutes,\n      cookieDomain,\n      cookieOptions\n    } = _ref2;\n    if (lookupCookie && typeof document !== 'undefined') {\n      cookie.create(lookupCookie, lng, cookieMinutes, cookieDomain, cookieOptions);\n    }\n  }\n};\n\nvar querystring = {\n  name: 'querystring',\n  // Deconstruct the options object and extract the lookupQuerystring property\n  lookup(_ref) {\n    let {\n      lookupQuerystring\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      let {\n        search\n      } = window.location;\n      if (!window.location.search && window.location.hash?.indexOf('?') > -1) {\n        search = window.location.hash.substring(window.location.hash.indexOf('?'));\n      }\n      const query = search.substring(1);\n      const params = query.split('&');\n      for (let i = 0; i < params.length; i++) {\n        const pos = params[i].indexOf('=');\n        if (pos > 0) {\n          const key = params[i].substring(0, pos);\n          if (key === lookupQuerystring) {\n            found = params[i].substring(pos + 1);\n          }\n        }\n      }\n    }\n    return found;\n  }\n};\n\nvar hash = {\n  name: 'hash',\n  // Deconstruct the options object and extract the lookupHash property and the lookupFromHashIndex property\n  lookup(_ref) {\n    let {\n      lookupHash,\n      lookupFromHashIndex\n    } = _ref;\n    let found;\n    if (typeof window !== 'undefined') {\n      const {\n        hash\n      } = window.location;\n      if (hash && hash.length > 2) {\n        const query = hash.substring(1);\n        if (lookupHash) {\n          const params = query.split('&');\n          for (let i = 0; i < params.length; i++) {\n            const pos = params[i].indexOf('=');\n            if (pos > 0) {\n              const key = params[i].substring(0, pos);\n              if (key === lookupHash) {\n                found = params[i].substring(pos + 1);\n              }\n            }\n          }\n        }\n        if (found) return found;\n        if (!found && lookupFromHashIndex > -1) {\n          const language = hash.match(/\\/([a-zA-Z-]*)/g);\n          if (!Array.isArray(language)) return undefined;\n          const index = typeof lookupFromHashIndex === 'number' ? lookupFromHashIndex : 0;\n          return language[index]?.replace('/', '');\n        }\n      }\n    }\n    return found;\n  }\n};\n\nlet hasLocalStorageSupport = null;\nconst localStorageAvailable = () => {\n  if (hasLocalStorageSupport !== null) return hasLocalStorageSupport;\n  try {\n    hasLocalStorageSupport = typeof window !== 'undefined' && window.localStorage !== null;\n    if (!hasLocalStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.localStorage.setItem(testKey, 'foo');\n    window.localStorage.removeItem(testKey);\n  } catch (e) {\n    hasLocalStorageSupport = false;\n  }\n  return hasLocalStorageSupport;\n};\nvar localStorage = {\n  name: 'localStorage',\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  lookup(_ref) {\n    let {\n      lookupLocalStorage\n    } = _ref;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      return window.localStorage.getItem(lookupLocalStorage) || undefined; // Undefined ensures type consistency with the previous version of this function\n    }\n    return undefined;\n  },\n  // Deconstruct the options object and extract the lookupLocalStorage property\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupLocalStorage\n    } = _ref2;\n    if (lookupLocalStorage && localStorageAvailable()) {\n      window.localStorage.setItem(lookupLocalStorage, lng);\n    }\n  }\n};\n\nlet hasSessionStorageSupport = null;\nconst sessionStorageAvailable = () => {\n  if (hasSessionStorageSupport !== null) return hasSessionStorageSupport;\n  try {\n    hasSessionStorageSupport = typeof window !== 'undefined' && window.sessionStorage !== null;\n    if (!hasSessionStorageSupport) {\n      return false;\n    }\n    const testKey = 'i18next.translate.boo';\n    window.sessionStorage.setItem(testKey, 'foo');\n    window.sessionStorage.removeItem(testKey);\n  } catch (e) {\n    hasSessionStorageSupport = false;\n  }\n  return hasSessionStorageSupport;\n};\nvar sessionStorage = {\n  name: 'sessionStorage',\n  lookup(_ref) {\n    let {\n      lookupSessionStorage\n    } = _ref;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      return window.sessionStorage.getItem(lookupSessionStorage) || undefined;\n    }\n    return undefined;\n  },\n  cacheUserLanguage(lng, _ref2) {\n    let {\n      lookupSessionStorage\n    } = _ref2;\n    if (lookupSessionStorage && sessionStorageAvailable()) {\n      window.sessionStorage.setItem(lookupSessionStorage, lng);\n    }\n  }\n};\n\nvar navigator$1 = {\n  name: 'navigator',\n  lookup(options) {\n    const found = [];\n    if (typeof navigator !== 'undefined') {\n      const {\n        languages,\n        userLanguage,\n        language\n      } = navigator;\n      if (languages) {\n        // chrome only; not an array, so can't use .push.apply instead of iterating\n        for (let i = 0; i < languages.length; i++) {\n          found.push(languages[i]);\n        }\n      }\n      if (userLanguage) {\n        found.push(userLanguage);\n      }\n      if (language) {\n        found.push(language);\n      }\n    }\n    return found.length > 0 ? found : undefined;\n  }\n};\n\nvar htmlTag = {\n  name: 'htmlTag',\n  // Deconstruct the options object and extract the htmlTag property\n  lookup(_ref) {\n    let {\n      htmlTag\n    } = _ref;\n    let found;\n    const internalHtmlTag = htmlTag || (typeof document !== 'undefined' ? document.documentElement : null);\n    if (internalHtmlTag && typeof internalHtmlTag.getAttribute === 'function') {\n      found = internalHtmlTag.getAttribute('lang');\n    }\n    return found;\n  }\n};\n\nvar path = {\n  name: 'path',\n  // Deconstruct the options object and extract the lookupFromPathIndex property\n  lookup(_ref) {\n    let {\n      lookupFromPathIndex\n    } = _ref;\n    if (typeof window === 'undefined') return undefined;\n    const language = window.location.pathname.match(/\\/([a-zA-Z-]*)/g);\n    if (!Array.isArray(language)) return undefined;\n    const index = typeof lookupFromPathIndex === 'number' ? lookupFromPathIndex : 0;\n    return language[index]?.replace('/', '');\n  }\n};\n\nvar subdomain = {\n  name: 'subdomain',\n  lookup(_ref) {\n    let {\n      lookupFromSubdomainIndex\n    } = _ref;\n    // If given get the subdomain index else 1\n    const internalLookupFromSubdomainIndex = typeof lookupFromSubdomainIndex === 'number' ? lookupFromSubdomainIndex + 1 : 1;\n    // get all matches if window.location. is existing\n    // first item of match is the match itself and the second is the first group match which should be the first subdomain match\n    // is the hostname no public domain get the or option of localhost\n    const language = typeof window !== 'undefined' && window.location?.hostname?.match(/^(\\w{2,5})\\.(([a-z0-9-]{1,63}\\.[a-z]{2,6})|localhost)/i);\n\n    // if there is no match (null) return undefined\n    if (!language) return undefined;\n    // return the given group match\n    return language[internalLookupFromSubdomainIndex];\n  }\n};\n\n// some environments, throws when accessing document.cookie\nlet canCookies = false;\ntry {\n  // eslint-disable-next-line no-unused-expressions\n  document.cookie;\n  canCookies = true;\n  // eslint-disable-next-line no-empty\n} catch (e) {}\nconst order = ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'];\nif (!canCookies) order.splice(1, 1);\nconst getDefaults = () => ({\n  order,\n  lookupQuerystring: 'lng',\n  lookupCookie: 'i18next',\n  lookupLocalStorage: 'i18nextLng',\n  lookupSessionStorage: 'i18nextLng',\n  // cache user language\n  caches: ['localStorage'],\n  excludeCacheFor: ['cimode'],\n  // cookieMinutes: 10,\n  // cookieDomain: 'myDomain'\n\n  convertDetectedLanguage: l => l\n});\nclass Browser {\n  constructor(services) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.type = 'languageDetector';\n    this.detectors = {};\n    this.init(services, options);\n  }\n  init() {\n    let services = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      languageUtils: {}\n    };\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let i18nOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.services = services;\n    this.options = defaults(options, this.options || {}, getDefaults());\n    if (typeof this.options.convertDetectedLanguage === 'string' && this.options.convertDetectedLanguage.indexOf('15897') > -1) {\n      this.options.convertDetectedLanguage = l => l.replace('-', '_');\n    }\n\n    // backwards compatibility\n    if (this.options.lookupFromUrlIndex) this.options.lookupFromPathIndex = this.options.lookupFromUrlIndex;\n    this.i18nOptions = i18nOptions;\n    this.addDetector(cookie$1);\n    this.addDetector(querystring);\n    this.addDetector(localStorage);\n    this.addDetector(sessionStorage);\n    this.addDetector(navigator$1);\n    this.addDetector(htmlTag);\n    this.addDetector(path);\n    this.addDetector(subdomain);\n    this.addDetector(hash);\n  }\n  addDetector(detector) {\n    this.detectors[detector.name] = detector;\n    return this;\n  }\n  detect() {\n    let detectionOrder = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.options.order;\n    let detected = [];\n    detectionOrder.forEach(detectorName => {\n      if (this.detectors[detectorName]) {\n        let lookup = this.detectors[detectorName].lookup(this.options);\n        if (lookup && typeof lookup === 'string') lookup = [lookup];\n        if (lookup) detected = detected.concat(lookup);\n      }\n    });\n    detected = detected.filter(d => d !== undefined && d !== null && !hasXSS(d)).map(d => this.options.convertDetectedLanguage(d));\n    if (this.services && this.services.languageUtils && this.services.languageUtils.getBestMatchFromCodes) return detected; // new i18next v19.5.0\n    return detected.length > 0 ? detected[0] : null; // a little backward compatibility\n  }\n  cacheUserLanguage(lng) {\n    let caches = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.options.caches;\n    if (!caches) return;\n    if (this.options.excludeCacheFor && this.options.excludeCacheFor.indexOf(lng) > -1) return;\n    caches.forEach(cacheName => {\n      if (this.detectors[cacheName]) this.detectors[cacheName].cacheUserLanguage(lng, this.options);\n    });\n  }\n}\nBrowser.type = 'languageDetector';\n\nexport { Browser as default };\n"], "mappings": ";;;AAAA,IAAM;AAAA,EACJ;AAAA,EACA;AACF,IAAI,CAAC;AACL,SAAS,SAAS,KAAK;AACrB,UAAQ,KAAK,MAAM,KAAK,WAAW,CAAC,GAAG,YAAU;AAC/C,QAAI,QAAQ;AACV,iBAAW,QAAQ,QAAQ;AACzB,YAAI,IAAI,IAAI,MAAM,OAAW,KAAI,IAAI,IAAI,OAAO,IAAI;AAAA,MACtD;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,OAAO,UAAU,SAAU,QAAO;AAGtC,QAAM,cAAc,CAAC,mBAAmB,wBAAwB,wBAAwB,4BAA4B,mBAAmB,iBAAiB,oBAAoB,cAAc,eAAe,qBAAqB,yBAAyB,qBAAqB,YAAY;AACxR,SAAO,YAAY,KAAK,aAAW,QAAQ,KAAK,KAAK,CAAC;AACxD;AAGA,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB,SAAU,MAAM,KAAK;AAC3C,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,IAChF,MAAM;AAAA,EACR;AACA,QAAM,MAAM;AACZ,QAAM,QAAQ,mBAAmB,GAAG;AACpC,MAAI,MAAM,GAAG,IAAI,IAAI,KAAK;AAC1B,MAAI,IAAI,SAAS,GAAG;AAClB,UAAM,SAAS,IAAI,SAAS;AAC5B,QAAI,OAAO,MAAM,MAAM,EAAG,OAAM,IAAI,MAAM,2BAA2B;AACrE,WAAO,aAAa,KAAK,MAAM,MAAM,CAAC;AAAA,EACxC;AACA,MAAI,IAAI,QAAQ;AACd,QAAI,CAAC,mBAAmB,KAAK,IAAI,MAAM,GAAG;AACxC,YAAM,IAAI,UAAU,0BAA0B;AAAA,IAChD;AACA,WAAO,YAAY,IAAI,MAAM;AAAA,EAC/B;AACA,MAAI,IAAI,MAAM;AACZ,QAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,GAAG;AACtC,YAAM,IAAI,UAAU,wBAAwB;AAAA,IAC9C;AACA,WAAO,UAAU,IAAI,IAAI;AAAA,EAC3B;AACA,MAAI,IAAI,SAAS;AACf,QAAI,OAAO,IAAI,QAAQ,gBAAgB,YAAY;AACjD,YAAM,IAAI,UAAU,2BAA2B;AAAA,IACjD;AACA,WAAO,aAAa,IAAI,QAAQ,YAAY,CAAC;AAAA,EAC/C;AACA,MAAI,IAAI,SAAU,QAAO;AACzB,MAAI,IAAI,OAAQ,QAAO;AACvB,MAAI,IAAI,UAAU;AAChB,UAAM,WAAW,OAAO,IAAI,aAAa,WAAW,IAAI,SAAS,YAAY,IAAI,IAAI;AACrF,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF,KAAK;AACH,eAAO;AACP;AAAA,MACF;AACE,cAAM,IAAI,UAAU,4BAA4B;AAAA,IACpD;AAAA,EACF;AACA,MAAI,IAAI,YAAa,QAAO;AAC5B,SAAO;AACT;AACA,IAAM,SAAS;AAAA,EACb,OAAO,MAAM,OAAO,SAAS,QAAQ;AACnC,QAAI,gBAAgB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MACtF,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AACA,QAAI,SAAS;AACX,oBAAc,UAAU,oBAAI,KAAK;AACjC,oBAAc,QAAQ,QAAQ,cAAc,QAAQ,QAAQ,IAAI,UAAU,KAAK,GAAI;AAAA,IACrF;AACA,QAAI,OAAQ,eAAc,SAAS;AACnC,aAAS,SAAS,gBAAgB,MAAM,OAAO,aAAa;AAAA,EAC9D;AAAA,EACA,KAAK,MAAM;AACT,UAAM,SAAS,GAAG,IAAI;AACtB,UAAM,KAAK,SAAS,OAAO,MAAM,GAAG;AACpC,aAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,UAAI,IAAI,GAAG,CAAC;AACZ,aAAO,EAAE,OAAO,CAAC,MAAM,IAAK,KAAI,EAAE,UAAU,GAAG,EAAE,MAAM;AACvD,UAAI,EAAE,QAAQ,MAAM,MAAM,EAAG,QAAO,EAAE,UAAU,OAAO,QAAQ,EAAE,MAAM;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM,QAAQ;AACnB,SAAK,OAAO,MAAM,IAAI,IAAI,MAAM;AAAA,EAClC;AACF;AACA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,OAAO,aAAa,aAAa;AACnD,aAAO,OAAO,KAAK,YAAY,KAAK;AAAA,IACtC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,KAAK,OAAO;AAC5B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,gBAAgB,OAAO,aAAa,aAAa;AACnD,aAAO,OAAO,cAAc,KAAK,eAAe,cAAc,aAAa;AAAA,IAC7E;AAAA,EACF;AACF;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,QAAI,OAAO,WAAW,aAAa;AACjC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,OAAO;AACX,UAAI,CAAC,OAAO,SAAS,UAAU,OAAO,SAAS,MAAM,QAAQ,GAAG,IAAI,IAAI;AACtE,iBAAS,OAAO,SAAS,KAAK,UAAU,OAAO,SAAS,KAAK,QAAQ,GAAG,CAAC;AAAA,MAC3E;AACA,YAAM,QAAQ,OAAO,UAAU,CAAC;AAChC,YAAM,SAAS,MAAM,MAAM,GAAG;AAC9B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,MAAM,OAAO,CAAC,EAAE,QAAQ,GAAG;AACjC,YAAI,MAAM,GAAG;AACX,gBAAM,MAAM,OAAO,CAAC,EAAE,UAAU,GAAG,GAAG;AACtC,cAAI,QAAQ,mBAAmB;AAC7B,oBAAQ,OAAO,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,QAAI,OAAO,WAAW,aAAa;AACjC,YAAM;AAAA,QACJ,MAAAA;AAAA,MACF,IAAI,OAAO;AACX,UAAIA,SAAQA,MAAK,SAAS,GAAG;AAC3B,cAAM,QAAQA,MAAK,UAAU,CAAC;AAC9B,YAAI,YAAY;AACd,gBAAM,SAAS,MAAM,MAAM,GAAG;AAC9B,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAM,MAAM,OAAO,CAAC,EAAE,QAAQ,GAAG;AACjC,gBAAI,MAAM,GAAG;AACX,oBAAM,MAAM,OAAO,CAAC,EAAE,UAAU,GAAG,GAAG;AACtC,kBAAI,QAAQ,YAAY;AACtB,wBAAQ,OAAO,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA,cACrC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAO,QAAO;AAClB,YAAI,CAAC,SAAS,sBAAsB,IAAI;AACtC,gBAAM,WAAWA,MAAK,MAAM,iBAAiB;AAC7C,cAAI,CAAC,MAAM,QAAQ,QAAQ,EAAG,QAAO;AACrC,gBAAM,QAAQ,OAAO,wBAAwB,WAAW,sBAAsB;AAC9E,iBAAO,SAAS,KAAK,GAAG,QAAQ,KAAK,EAAE;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,yBAAyB;AAC7B,IAAM,wBAAwB,MAAM;AAClC,MAAI,2BAA2B,KAAM,QAAO;AAC5C,MAAI;AACF,6BAAyB,OAAO,WAAW,eAAe,OAAO,iBAAiB;AAClF,QAAI,CAAC,wBAAwB;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,UAAU;AAChB,WAAO,aAAa,QAAQ,SAAS,KAAK;AAC1C,WAAO,aAAa,WAAW,OAAO;AAAA,EACxC,SAAS,GAAG;AACV,6BAAyB;AAAA,EAC3B;AACA,SAAO;AACT;AACA,IAAI,eAAe;AAAA,EACjB,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,sBAAsB,sBAAsB,GAAG;AACjD,aAAO,OAAO,aAAa,QAAQ,kBAAkB,KAAK;AAAA,IAC5D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,KAAK,OAAO;AAC5B,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,sBAAsB,sBAAsB,GAAG;AACjD,aAAO,aAAa,QAAQ,oBAAoB,GAAG;AAAA,IACrD;AAAA,EACF;AACF;AAEA,IAAI,2BAA2B;AAC/B,IAAM,0BAA0B,MAAM;AACpC,MAAI,6BAA6B,KAAM,QAAO;AAC9C,MAAI;AACF,+BAA2B,OAAO,WAAW,eAAe,OAAO,mBAAmB;AACtF,QAAI,CAAC,0BAA0B;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,UAAU;AAChB,WAAO,eAAe,QAAQ,SAAS,KAAK;AAC5C,WAAO,eAAe,WAAW,OAAO;AAAA,EAC1C,SAAS,GAAG;AACV,+BAA2B;AAAA,EAC7B;AACA,SAAO;AACT;AACA,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,wBAAwB,wBAAwB,GAAG;AACrD,aAAO,OAAO,eAAe,QAAQ,oBAAoB,KAAK;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,KAAK,OAAO;AAC5B,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,wBAAwB,wBAAwB,GAAG;AACrD,aAAO,eAAe,QAAQ,sBAAsB,GAAG;AAAA,IACzD;AAAA,EACF;AACF;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,OAAO,SAAS;AACd,UAAM,QAAQ,CAAC;AACf,QAAI,OAAO,cAAc,aAAa;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,WAAW;AAEb,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAM,KAAK,UAAU,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AACA,UAAI,cAAc;AAChB,cAAM,KAAK,YAAY;AAAA,MACzB;AACA,UAAI,UAAU;AACZ,cAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF;AACA,WAAO,MAAM,SAAS,IAAI,QAAQ;AAAA,EACpC;AACF;AAEA,IAAI,UAAU;AAAA,EACZ,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF,SAAAC;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,UAAM,kBAAkBA,aAAY,OAAO,aAAa,cAAc,SAAS,kBAAkB;AACjG,QAAI,mBAAmB,OAAO,gBAAgB,iBAAiB,YAAY;AACzE,cAAQ,gBAAgB,aAAa,MAAM;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA;AAAA,EAEN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,WAAW,YAAa,QAAO;AAC1C,UAAM,WAAW,OAAO,SAAS,SAAS,MAAM,iBAAiB;AACjE,QAAI,CAAC,MAAM,QAAQ,QAAQ,EAAG,QAAO;AACrC,UAAM,QAAQ,OAAO,wBAAwB,WAAW,sBAAsB;AAC9E,WAAO,SAAS,KAAK,GAAG,QAAQ,KAAK,EAAE;AAAA,EACzC;AACF;AAEA,IAAI,YAAY;AAAA,EACd,MAAM;AAAA,EACN,OAAO,MAAM;AACX,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,UAAM,mCAAmC,OAAO,6BAA6B,WAAW,2BAA2B,IAAI;AAIvH,UAAM,WAAW,OAAO,WAAW,eAAe,OAAO,UAAU,UAAU,MAAM,wDAAwD;AAG3I,QAAI,CAAC,SAAU,QAAO;AAEtB,WAAO,SAAS,gCAAgC;AAAA,EAClD;AACF;AAGA,IAAI,aAAa;AACjB,IAAI;AAEF,WAAS;AACT,eAAa;AAEf,SAAS,GAAG;AAAC;AACb,IAAM,QAAQ,CAAC,eAAe,UAAU,gBAAgB,kBAAkB,aAAa,SAAS;AAChG,IAAI,CAAC,WAAY,OAAM,OAAO,GAAG,CAAC;AAClC,IAAM,cAAc,OAAO;AAAA,EACzB;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,sBAAsB;AAAA;AAAA,EAEtB,QAAQ,CAAC,cAAc;AAAA,EACvB,iBAAiB,CAAC,QAAQ;AAAA;AAAA;AAAA,EAI1B,yBAAyB,OAAK;AAChC;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAY,UAAU;AACpB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,SAAK,OAAO;AACZ,SAAK,YAAY,CAAC;AAClB,SAAK,KAAK,UAAU,OAAO;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,QAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAAA,MACjF,eAAe,CAAC;AAAA,IAClB;AACA,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACvF,SAAK,WAAW;AAChB,SAAK,UAAU,SAAS,SAAS,KAAK,WAAW,CAAC,GAAG,YAAY,CAAC;AAClE,QAAI,OAAO,KAAK,QAAQ,4BAA4B,YAAY,KAAK,QAAQ,wBAAwB,QAAQ,OAAO,IAAI,IAAI;AAC1H,WAAK,QAAQ,0BAA0B,OAAK,EAAE,QAAQ,KAAK,GAAG;AAAA,IAChE;AAGA,QAAI,KAAK,QAAQ,mBAAoB,MAAK,QAAQ,sBAAsB,KAAK,QAAQ;AACrF,SAAK,cAAc;AACnB,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,WAAW;AAC5B,SAAK,YAAY,YAAY;AAC7B,SAAK,YAAY,cAAc;AAC/B,SAAK,YAAY,WAAW;AAC5B,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,IAAI;AACrB,SAAK,YAAY,SAAS;AAC1B,SAAK,YAAY,IAAI;AAAA,EACvB;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,UAAU,SAAS,IAAI,IAAI;AAChC,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,QAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,KAAK,QAAQ;AACtG,QAAI,WAAW,CAAC;AAChB,mBAAe,QAAQ,kBAAgB;AACrC,UAAI,KAAK,UAAU,YAAY,GAAG;AAChC,YAAI,SAAS,KAAK,UAAU,YAAY,EAAE,OAAO,KAAK,OAAO;AAC7D,YAAI,UAAU,OAAO,WAAW,SAAU,UAAS,CAAC,MAAM;AAC1D,YAAI,OAAQ,YAAW,SAAS,OAAO,MAAM;AAAA,MAC/C;AAAA,IACF,CAAC;AACD,eAAW,SAAS,OAAO,OAAK,MAAM,UAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,OAAK,KAAK,QAAQ,wBAAwB,CAAC,CAAC;AAC7H,QAAI,KAAK,YAAY,KAAK,SAAS,iBAAiB,KAAK,SAAS,cAAc,sBAAuB,QAAO;AAC9G,WAAO,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,EAC7C;AAAA,EACA,kBAAkB,KAAK;AACrB,QAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,KAAK,QAAQ;AAC9F,QAAI,CAAC,OAAQ;AACb,QAAI,KAAK,QAAQ,mBAAmB,KAAK,QAAQ,gBAAgB,QAAQ,GAAG,IAAI,GAAI;AACpF,WAAO,QAAQ,eAAa;AAC1B,UAAI,KAAK,UAAU,SAAS,EAAG,MAAK,UAAU,SAAS,EAAE,kBAAkB,KAAK,KAAK,OAAO;AAAA,IAC9F,CAAC;AAAA,EACH;AACF;AACA,QAAQ,OAAO;", "names": ["hash", "htmlTag"]}